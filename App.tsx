import React, { useState, useEffect } from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createStackNavigator } from '@react-navigation/stack';
import { StatusBar } from 'expo-status-bar';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { View, Text, StyleSheet, Animated } from 'react-native';

// Import screens
import HomeScreen from './components/screens/HomeScreen';
import MenuScreen from './components/screens/MenuScreen';
import CartScreen from './components/screens/CartScreen';
import ProfileScreen from './components/screens/ProfileScreen';
import ItemDetailScreen from './components/screens/ItemDetailScreen';
import OrderTrackingScreen from './components/screens/OrderTrackingScreen';
import EventsScreen from './components/screens/EventsScreen';
import LoadingScreen from './delivery-loading-screen';

// Types
import { CartItem } from './types';

const Tab = createBottomTabNavigator();
const Stack = createStackNavigator();

// Custom Tab Bar Icon Component
const TabIcon = ({ focused, icon, label }: { focused: boolean; icon: string; label: string }) => (
  <View style={styles.tabIcon}>
    <View style={[styles.iconContainer, focused && styles.activeIcon]}>
      <Text style={styles.iconText}>{icon}</Text>
    </View>
    <Text style={[styles.tabLabel, focused && styles.activeLabel]}>{label}</Text>
  </View>
);

// Main Tab Navigator
function MainTabs() {
  return (
    <Tab.Navigator
      screenOptions={{
        headerShown: false,
        tabBarStyle: styles.tabBar,
        tabBarShowLabel: false,
      }}
    >
      <Tab.Screen
        name="Home"
        component={HomeScreen}
        options={{
          tabBarIcon: ({ focused }) => (
            <TabIcon focused={focused} icon="🏠" label="Home" />
          ),
        }}
      />
      <Tab.Screen
        name="Menu"
        component={MenuScreen}
        options={{
          tabBarIcon: ({ focused }) => (
            <TabIcon focused={focused} icon="📋" label="Menu" />
          ),
        }}
      />
      <Tab.Screen
        name="Cart"
        component={CartScreen}
        options={{
          tabBarIcon: ({ focused }) => (
            <TabIcon focused={focused} icon="🛒" label="Cart" />
          ),
        }}
      />
      <Tab.Screen
        name="Events"
        component={EventsScreen}
        options={{
          tabBarIcon: ({ focused }) => (
            <TabIcon focused={focused} icon="🎫" label="Events" />
          ),
        }}
      />
      <Tab.Screen
        name="Profile"
        component={ProfileScreen}
        options={{
          tabBarIcon: ({ focused }) => (
            <TabIcon focused={focused} icon="👤" label="Profile" />
          ),
        }}
      />
    </Tab.Navigator>
  );
}

// Root Stack Navigator
function RootStack() {
  return (
    <Stack.Navigator screenOptions={{ headerShown: false }}>
      <Stack.Screen name="MainTabs" component={MainTabs} />
      <Stack.Screen name="ItemDetail" component={ItemDetailScreen} />
      <Stack.Screen name="OrderTracking" component={OrderTrackingScreen} />
    </Stack.Navigator>
  );
}

export default function App() {
  const [isLoading, setIsLoading] = useState(true);
  const [cart, setCart] = useState<CartItem[]>([]);
  const fadeAnim = useState(new Animated.Value(1))[0];

  useEffect(() => {
    // Simulate app initialization
    const timer = setTimeout(() => {
      // Fade out animation before transitioning
      Animated.timing(fadeAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }).start(() => {
        setIsLoading(false);
      });
    }, 2200); // Reduced to account for fade animation

    return () => clearTimeout(timer);
  }, [fadeAnim]);

  const addToCart = (item: CartItem) => {
    setCart(prevCart => {
      const existingItem = prevCart.find(cartItem => 
        cartItem.menuItem.id === item.menuItem.id &&
        JSON.stringify(cartItem.customizations) === JSON.stringify(item.customizations)
      );

      if (existingItem) {
        return prevCart.map(cartItem =>
          cartItem.id === existingItem.id
            ? { ...cartItem, quantity: cartItem.quantity + item.quantity }
            : cartItem
        );
      }

      return [...prevCart, { ...item, id: `${item.menuItem.id}-${Date.now()}` }];
    });
  };

  const removeFromCart = (itemId: string) => {
    setCart(prevCart => prevCart.filter(item => item.id !== itemId));
  };

  const updateCartItemQuantity = (itemId: string, quantity: number) => {
    if (quantity <= 0) {
      removeFromCart(itemId);
      return;
    }

    setCart(prevCart =>
      prevCart.map(item =>
        item.id === itemId ? { ...item, quantity } : item
      )
    );
  };

  const clearCart = () => {
    setCart([]);
  };

  if (isLoading) {
    return (
      <Animated.View style={[{ flex: 1 }, { opacity: fadeAnim }]}>
        <LoadingScreen />
      </Animated.View>
    );
  }

  return (
    <SafeAreaProvider>
      <NavigationContainer>
        <StatusBar style="auto" />
        <RootStack />
      </NavigationContainer>
    </SafeAreaProvider>
  );
}

const styles = StyleSheet.create({
  tabBar: {
    backgroundColor: '#ffffff',
    borderTopWidth: 1,
    borderTopColor: '#e5e7eb',
    paddingTop: 8,
    paddingBottom: 20,
    height: 80,
  },
  tabIcon: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  iconContainer: {
    width: 24,
    height: 24,
    borderRadius: 4,
    backgroundColor: '#d1d5db',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 4,
  },
  activeIcon: {
    backgroundColor: '#dc2626',
  },
  iconText: {
    fontSize: 12,
  },
  tabLabel: {
    fontSize: 10,
    fontWeight: '500',
    color: '#6b7280',
    fontFamily: 'Lufga, system-ui, sans-serif',
  },
  activeLabel: {
    color: '#dc2626',
    fontWeight: '600',
  },
});
