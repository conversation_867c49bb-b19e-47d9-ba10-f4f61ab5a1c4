import { MenuItem, Event, User, Address, PaymentMethod } from '../types';

// Placeholder image URLs (in production, these would be local assets)
const foodImages = {
  margherita: 'https://images.unsplash.com/photo-1604382354936-07c5d9983bd3?w=800&h=600&fit=crop',
  quattroStagioni: 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=800&h=600&fit=crop',
  carbonara: 'https://images.unsplash.com/photo-1621996346565-e3dbc353d2e5?w=800&h=600&fit=crop',
  caesarSalad: 'https://images.unsplash.com/photo-1546793665-c74683f339c1?w=800&h=600&fit=crop',
  tiramisu: 'https://images.unsplash.com/photo-1571877227200-a0d98ea607e9?w=800&h=600&fit=crop',
  pepperoni: 'https://images.unsplash.com/photo-1628840042765-356cda07504e?w=800&h=600&fit=crop',
  bolognese: 'https://images.unsplash.com/photo-1551183053-bf91a1d81141?w=800&h=600&fit=crop',
  gelato: 'https://images.unsplash.com/photo-1567206563064-6f60f40a2b57?w=800&h=600&fit=crop',
  wine: 'https://images.unsplash.com/photo-1506377247377-2a5b3b417ebb?w=800&h=600&fit=crop',
  bruschetta: 'https://images.unsplash.com/photo-1572441713132-51c75654db73?w=800&h=600&fit=crop',
};

const eventImages = {
  wineTasting: 'https://images.unsplash.com/photo-1510812431401-41d2bd2722f3?w=1200&h=800&fit=crop',
  cookingClass: 'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=1200&h=800&fit=crop',
  liveMusic: 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=1200&h=800&fit=crop',
  privateDining: 'https://images.unsplash.com/photo-1414235077428-338989a2e8c0?w=1200&h=800&fit=crop',
  pizzaMaking: 'https://images.unsplash.com/photo-1513104890138-7c749659a591?w=1200&h=800&fit=crop',
};

export const mockMenuItems: MenuItem[] = [
  {
    id: '1',
    name: 'Margherita Classica',
    description: 'Fresh mozzarella, basil, tomato sauce on traditional thin crust',
    price: 89,
    image: foodImages.margherita,
    category: 'pizza',
    rating: 4.8,
    prepTime: '15-20 min',
    ingredients: ['Mozzarella', 'Fresh Basil', 'Tomato Sauce', 'Olive Oil'],
    allergens: ['Gluten', 'Dairy'],
    isVegetarian: true,
    isVegan: false,
    isGlutenFree: false,
    isFeatured: true,
    isPromotional: false,
    customizations: [
      {
        id: 'size',
        name: 'Size',
        type: 'size',
        required: true,
        options: [
          { id: 'small', name: 'Small (8")', price: 0, isDefault: true },
          { id: 'medium', name: 'Medium (10")', price: 20 },
          { id: 'large', name: 'Large (12")', price: 40 }
        ]
      },
      {
        id: 'crust',
        name: 'Crust Type',
        type: 'crust',
        required: true,
        options: [
          { id: 'thin', name: 'Thin Crust', price: 0, isDefault: true },
          { id: 'thick', name: 'Thick Crust', price: 10 },
          { id: 'stuffed', name: 'Cheese Stuffed', price: 25 }
        ]
      }
    ]
  },
  {
    id: '2',
    name: 'Quattro Stagioni',
    description: 'Mushrooms, ham, artichokes, olives - representing the four seasons',
    price: 125,
    image: foodImages.quattroStagioni,
    category: 'pizza',
    rating: 4.9,
    prepTime: '18-25 min',
    ingredients: ['Mozzarella', 'Ham', 'Mushrooms', 'Artichokes', 'Olives', 'Tomato Sauce'],
    allergens: ['Gluten', 'Dairy'],
    isVegetarian: false,
    isVegan: false,
    isGlutenFree: false,
    isFeatured: false,
    isPromotional: true
  },
  {
    id: '3',
    name: 'Pasta Carbonara',
    description: 'Creamy sauce with pancetta, parmesan, and fresh black pepper',
    price: 95,
    image: foodImages.carbonara,
    category: 'pasta',
    rating: 4.7,
    prepTime: '12-18 min',
    ingredients: ['Spaghetti', 'Pancetta', 'Parmesan', 'Eggs', 'Black Pepper'],
    allergens: ['Gluten', 'Dairy', 'Eggs'],
    isVegetarian: false,
    isVegan: false,
    isGlutenFree: false,
    isFeatured: true,
    isPromotional: false
  },
  {
    id: '4',
    name: 'Caesar Salad',
    description: 'Crisp romaine lettuce, parmesan, croutons, classic caesar dressing',
    price: 65,
    image: foodImages.caesarSalad,
    category: 'salads',
    rating: 4.5,
    prepTime: '8-12 min',
    ingredients: ['Romaine Lettuce', 'Parmesan', 'Croutons', 'Caesar Dressing'],
    allergens: ['Gluten', 'Dairy'],
    isVegetarian: true,
    isVegan: false,
    isGlutenFree: false,
    isFeatured: false,
    isPromotional: false
  },
  {
    id: '5',
    name: 'Tiramisu',
    description: 'Classic Italian dessert with coffee-soaked ladyfingers and mascarpone',
    price: 55,
    image: foodImages.tiramisu,
    category: 'desserts',
    rating: 4.9,
    prepTime: '5 min',
    ingredients: ['Ladyfingers', 'Mascarpone', 'Coffee', 'Cocoa', 'Sugar'],
    allergens: ['Gluten', 'Dairy', 'Eggs'],
    isVegetarian: true,
    isVegan: false,
    isGlutenFree: false
  },
  {
    id: '6',
    name: 'Italian Soda',
    description: 'Refreshing sparkling water with natural fruit syrups',
    price: 25,
    image: '🥤',
    category: 'drinks',
    rating: 4.3,
    prepTime: '2 min',
    ingredients: ['Sparkling Water', 'Natural Fruit Syrup'],
    allergens: [],
    isVegetarian: true,
    isVegan: true,
    isGlutenFree: true
  }
];

export const mockEvents: Event[] = [
  {
    id: '1',
    title: 'Wine & Dine Evening',
    description: 'An exclusive evening featuring Italian wines paired with our signature dishes',
    date: new Date('2024-07-15'),
    startTime: '19:00',
    endTime: '22:00',
    location: 'Le Pizzeria Main Dining Room',
    price: 350,
    capacity: 30,
    availableTickets: 12,
    image: '🍷',
    category: 'wine_tasting',
    features: ['Wine Pairing', '4-Course Meal', 'Sommelier Guide', 'Live Jazz']
  },
  {
    id: '2',
    title: 'Pizza Making Masterclass',
    description: 'Learn the art of authentic Italian pizza making from our head chef',
    date: new Date('2024-07-20'),
    startTime: '14:00',
    endTime: '17:00',
    location: 'Le Pizzeria Kitchen',
    price: 180,
    capacity: 15,
    availableTickets: 8,
    image: '👨‍🍳',
    category: 'cooking_class',
    features: ['Hands-on Cooking', 'Recipe Book', 'Lunch Included', 'Certificate']
  }
];

export const mockUser: User = {
  id: '1',
  name: 'John Doe',
  email: '<EMAIL>',
  phone: '+27 82 123 4567',
  addresses: [
    {
      id: '1',
      label: 'Home',
      street: '123 Observatory Road',
      city: 'Observatory, Cape Town',
      postalCode: '7925',
      country: 'South Africa',
      isDefault: true
    }
  ],
  paymentMethods: [
    {
      id: '1',
      type: 'apple_pay',
      isDefault: true
    }
  ],
  orderHistory: [],
  favorites: ['1', '3'],
  preferences: {
    notifications: true,
    darkMode: false,
    language: 'en',
    dietaryRestrictions: []
  }
};

export const categories = [
  { id: 'popular', name: 'Popular', icon: '⭐' },
  { id: 'pizza', name: 'Pizza', icon: '🍕' },
  { id: 'pasta', name: 'Pasta', icon: '🍝' },
  { id: 'salads', name: 'Salads', icon: '🥗' },
  { id: 'desserts', name: 'Desserts', icon: '🍰' },
  { id: 'drinks', name: 'Drinks', icon: '🥤' }
];
