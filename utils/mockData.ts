import { MenuItem, Event, User, Address, PaymentMethod } from '../types';

export const mockMenuItems: MenuItem[] = [
  {
    id: '1',
    name: 'Margherita Classica',
    description: 'Fresh mozzarella, basil, tomato sauce on traditional thin crust',
    price: 89,
    image: '🍕',
    category: 'pizza',
    rating: 4.8,
    prepTime: '15-20 min',
    ingredients: ['Mozzarella', 'Fresh Basil', 'Tomato Sauce', 'Olive Oil'],
    allergens: ['Gluten', 'Dairy'],
    isVegetarian: true,
    isVegan: false,
    isGlutenFree: false,
    customizations: [
      {
        id: 'size',
        name: '<PERSON><PERSON>',
        type: 'size',
        required: true,
        options: [
          { id: 'small', name: 'Small (8")', price: 0, isDefault: true },
          { id: 'medium', name: 'Medium (10")', price: 20 },
          { id: 'large', name: 'Large (12")', price: 40 }
        ]
      },
      {
        id: 'crust',
        name: 'Crust Type',
        type: 'crust',
        required: true,
        options: [
          { id: 'thin', name: 'Thin Crust', price: 0, isDefault: true },
          { id: 'thick', name: 'Thick Crust', price: 10 },
          { id: 'stuffed', name: 'Cheese Stuffed', price: 25 }
        ]
      }
    ]
  },
  {
    id: '2',
    name: 'Quattro Stagioni',
    description: 'Mushrooms, ham, artichokes, olives - representing the four seasons',
    price: 125,
    image: '🍕',
    category: 'pizza',
    rating: 4.9,
    prepTime: '18-25 min',
    ingredients: ['Mozzarella', 'Ham', 'Mushrooms', 'Artichokes', 'Olives', 'Tomato Sauce'],
    allergens: ['Gluten', 'Dairy'],
    isVegetarian: false,
    isVegan: false,
    isGlutenFree: false
  },
  {
    id: '3',
    name: 'Pasta Carbonara',
    description: 'Creamy sauce with pancetta, parmesan, and fresh black pepper',
    price: 95,
    image: '🍝',
    category: 'pasta',
    rating: 4.7,
    prepTime: '12-18 min',
    ingredients: ['Spaghetti', 'Pancetta', 'Parmesan', 'Eggs', 'Black Pepper'],
    allergens: ['Gluten', 'Dairy', 'Eggs'],
    isVegetarian: false,
    isVegan: false,
    isGlutenFree: false
  },
  {
    id: '4',
    name: 'Caesar Salad',
    description: 'Crisp romaine lettuce, parmesan, croutons, classic caesar dressing',
    price: 65,
    image: '🥗',
    category: 'salads',
    rating: 4.5,
    prepTime: '8-12 min',
    ingredients: ['Romaine Lettuce', 'Parmesan', 'Croutons', 'Caesar Dressing'],
    allergens: ['Gluten', 'Dairy'],
    isVegetarian: true,
    isVegan: false,
    isGlutenFree: false
  },
  {
    id: '5',
    name: 'Tiramisu',
    description: 'Classic Italian dessert with coffee-soaked ladyfingers and mascarpone',
    price: 55,
    image: '🍰',
    category: 'desserts',
    rating: 4.9,
    prepTime: '5 min',
    ingredients: ['Ladyfingers', 'Mascarpone', 'Coffee', 'Cocoa', 'Sugar'],
    allergens: ['Gluten', 'Dairy', 'Eggs'],
    isVegetarian: true,
    isVegan: false,
    isGlutenFree: false
  },
  {
    id: '6',
    name: 'Italian Soda',
    description: 'Refreshing sparkling water with natural fruit syrups',
    price: 25,
    image: foodImages.wine,
    category: 'beverages',
    rating: 4.3,
    prepTime: '2 min',
    ingredients: ['Sparkling Water', 'Natural Fruit Syrup'],
    allergens: [],
    isVegetarian: true,
    isVegan: true,
    isGlutenFree: true
  },
  // Le Pizzeria Tapas
  {
    id: '7',
    name: 'Bruschetta Trio',
    description: 'Three varieties of toasted bread with fresh toppings',
    price: 65,
    image: foodImages.bruschetta,
    category: 'tapas',
    rating: 4.6,
    prepTime: '8-12 min',
    ingredients: ['Sourdough Bread', 'Tomatoes', 'Basil', 'Mozzarella'],
    allergens: ['Gluten', 'Dairy'],
    isVegetarian: true,
    isVegan: false,
    isGlutenFree: false,
    isFeatured: false,
    isPromotional: false,
    popularRank: 3,
    orderFrequency: 'Perfect starter'
  },
  {
    id: '8',
    name: 'Antipasti Board',
    description: 'Selection of Italian cured meats, cheeses, and olives',
    price: 125,
    image: foodImages.bruschetta,
    category: 'tapas',
    rating: 4.8,
    prepTime: '5 min',
    ingredients: ['Prosciutto', 'Salami', 'Mozzarella', 'Olives', 'Bread'],
    allergens: ['Gluten', 'Dairy'],
    isVegetarian: false,
    isVegan: false,
    isGlutenFree: false,
    isFeatured: false,
    isPromotional: false
  },
  {
    id: '9',
    name: 'Garlic Bread',
    description: 'Crispy bread with garlic butter and herbs',
    price: 45,
    image: foodImages.bruschetta,
    category: 'tapas',
    rating: 4.4,
    prepTime: '6-8 min',
    ingredients: ['Bread', 'Garlic', 'Butter', 'Herbs'],
    allergens: ['Gluten', 'Dairy'],
    isVegetarian: true,
    isVegan: false,
    isGlutenFree: false
  }
];

export const mockEvents: Event[] = [
  {
    id: '1',
    title: 'Wine & Dine Evening',
    description: 'An exclusive evening featuring Italian wines paired with our signature dishes',
    date: new Date('2024-07-15'),
    startTime: '19:00',
    endTime: '22:00',
    location: 'Le Pizzeria Main Dining Room',
    price: 350,
    capacity: 30,
    availableTickets: 12,
    image: '🍷',
    category: 'wine_tasting',
    features: ['Wine Pairing', '4-Course Meal', 'Sommelier Guide', 'Live Jazz']
  },
  {
    id: '2',
    title: 'Pizza Making Masterclass',
    description: 'Learn the art of authentic Italian pizza making from our head chef',
    date: new Date('2024-07-20'),
    startTime: '14:00',
    endTime: '17:00',
    location: 'Le Pizzeria Kitchen',
    price: 180,
    capacity: 15,
    availableTickets: 8,
    image: '👨‍🍳',
    category: 'cooking_class',
    features: ['Hands-on Cooking', 'Recipe Book', 'Lunch Included', 'Certificate']
  }
];

export const mockUser: User = {
  id: '1',
  name: 'John Doe',
  email: '<EMAIL>',
  phone: '+27 82 123 4567',
  addresses: [
    {
      id: '1',
      label: 'Home',
      street: '123 Observatory Road',
      city: 'Observatory, Cape Town',
      postalCode: '7925',
      country: 'South Africa',
      isDefault: true
    }
  ],
  paymentMethods: [
    {
      id: '1',
      type: 'apple_pay',
      isDefault: true
    }
  ],
  orderHistory: [],
  favorites: ['1', '3'],
  preferences: {
    notifications: true,
    darkMode: false,
    language: 'en',
    dietaryRestrictions: []
  }
};

export const categories = [
  { id: 'popular', name: 'Popular', icon: '⭐' },
  { id: 'tapas', name: 'Le Pizzeria Tapas', icon: '🥖' },
  { id: 'pizza', name: 'Le Pizzeria Pizzas', icon: '🍕' },
  { id: 'pasta', name: 'Pastas', icon: '🍝' },
  { id: 'extras', name: 'Extras', icon: '🥗' },
  { id: 'drinks', name: 'Drinks', icon: '🥤' }
];
