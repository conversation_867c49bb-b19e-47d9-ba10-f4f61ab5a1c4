import { MenuItem, Event } from '../types';

// Debounce function for search performance
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  delay: number
): ((...args: Parameters<T>) => void) => {
  let timeoutId: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func(...args), delay);
  };
};

// Search function for menu items
export const searchMenuItems = (
  items: MenuItem[],
  query: string
): MenuItem[] => {
  if (!query.trim()) return items;

  const searchTerm = query.toLowerCase().trim();
  
  return items.filter(item => {
    // Search in name
    if (item.name.toLowerCase().includes(searchTerm)) return true;
    
    // Search in description
    if (item.description.toLowerCase().includes(searchTerm)) return true;
    
    // Search in category
    if (item.category.toLowerCase().includes(searchTerm)) return true;
    
    // Search in ingredients
    if (item.ingredients.some(ingredient => 
      ingredient.toLowerCase().includes(searchTerm)
    )) return true;
    
    return false;
  });
};

// Search function for events
export const searchEvents = (
  events: Event[],
  query: string
): Event[] => {
  if (!query.trim()) return events;

  const searchTerm = query.toLowerCase().trim();
  
  return events.filter(event => {
    // Search in title
    if (event.title.toLowerCase().includes(searchTerm)) return true;
    
    // Search in description
    if (event.description.toLowerCase().includes(searchTerm)) return true;
    
    // Search in category
    if (event.category.toLowerCase().includes(searchTerm)) return true;
    
    // Search in event type
    if (event.eventType?.toLowerCase().includes(searchTerm)) return true;
    
    // Search in location
    if (event.location.toLowerCase().includes(searchTerm)) return true;
    
    return false;
  });
};

// Highlight search terms in text
export const highlightSearchTerm = (
  text: string,
  searchTerm: string
): { text: string; isHighlighted: boolean }[] => {
  if (!searchTerm.trim()) {
    return [{ text, isHighlighted: false }];
  }

  const regex = new RegExp(`(${searchTerm})`, 'gi');
  const parts = text.split(regex);
  
  return parts.map(part => ({
    text: part,
    isHighlighted: regex.test(part),
  }));
};
