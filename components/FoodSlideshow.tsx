import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  Image,
  Dimensions,
  Animated,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { MenuItem } from '../types';
import { Colors, Typography, Spacing, Shadows, BorderRadius } from '../constants/theme';

const { width } = Dimensions.get('window');

interface FoodSlideshowProps {
  items: MenuItem[];
  onItemPress: (item: MenuItem) => void;
  autoPlayInterval?: number;
}

export default function FoodSlideshow({
  items,
  onItemPress,
  autoPlayInterval = 4000,
}: FoodSlideshowProps) {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isPaused, setIsPaused] = useState(false);
  const scrollViewRef = useRef<ScrollView>(null);
  const fadeAnim = useRef(new Animated.Value(1)).current;

  // Auto-play functionality
  useEffect(() => {
    if (isPaused || items.length <= 1) return;

    const interval = setInterval(() => {
      // Fade out animation
      Animated.timing(fadeAnim, {
        toValue: 0.7,
        duration: 200,
        useNativeDriver: true,
      }).start(() => {
        // Change slide
        const nextIndex = (currentIndex + 1) % items.length;
        setCurrentIndex(nextIndex);
        
        // Scroll to next item
        scrollViewRef.current?.scrollTo({
          x: nextIndex * width,
          animated: true,
        });

        // Fade in animation
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 200,
          useNativeDriver: true,
        }).start();
      });
    }, autoPlayInterval);

    return () => clearInterval(interval);
  }, [currentIndex, isPaused, items.length, autoPlayInterval, fadeAnim]);

  const handleScroll = (event: any) => {
    const scrollPosition = event.nativeEvent.contentOffset.x;
    const index = Math.round(scrollPosition / width);
    setCurrentIndex(index);
  };

  const handleTouchStart = () => {
    setIsPaused(true);
  };

  const handleTouchEnd = () => {
    // Resume auto-play after 3 seconds of no interaction
    setTimeout(() => {
      setIsPaused(false);
    }, 3000);
  };

  const renderSlide = (item: MenuItem, index: number) => (
    <TouchableOpacity
      key={item.id}
      style={styles.slide}
      onPress={() => onItemPress(item)}
      onPressIn={handleTouchStart}
      onPressOut={handleTouchEnd}
    >
      <Animated.View style={[styles.slideContent, { opacity: fadeAnim }]}>
        <Image
          source={item.imageUri || { uri: item.image }}
          style={styles.slideImage}
          resizeMode="cover"
        />
        
        <LinearGradient
          colors={['transparent', 'rgba(0,0,0,0.4)', 'rgba(0,0,0,0.8)']}
          style={styles.slideOverlay}
        >
          <View style={styles.slideTextContainer}>
            <Text style={styles.slideTitle}>{item.name}</Text>
            <Text style={styles.slidePrice}>R{item.price}</Text>
            
            {/* Featured/Promotional indicators */}
            <View style={styles.slideBadges}>
              {item.isFeatured && (
                <View style={[styles.slideBadge, styles.featuredBadge]}>
                  <Text style={styles.slideBadgeText}>Featured</Text>
                </View>
              )}
              {item.isPromotional && (
                <View style={[styles.slideBadge, styles.promotionalBadge]}>
                  <Text style={styles.slideBadgeText}>Special Offer</Text>
                </View>
              )}
            </View>
          </View>
        </LinearGradient>
      </Animated.View>
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      <ScrollView
        ref={scrollViewRef}
        horizontal
        pagingEnabled
        showsHorizontalScrollIndicator={false}
        onScroll={handleScroll}
        scrollEventThrottle={16}
        onTouchStart={handleTouchStart}
        onTouchEnd={handleTouchEnd}
      >
        {items.map(renderSlide)}
      </ScrollView>

      {/* Pagination Dots */}
      <View style={styles.pagination}>
        {items.map((_, index) => (
          <TouchableOpacity
            key={index}
            style={[
              styles.paginationDot,
              index === currentIndex && styles.paginationDotActive,
            ]}
            onPress={() => {
              setCurrentIndex(index);
              scrollViewRef.current?.scrollTo({
                x: index * width,
                animated: true,
              });
            }}
          />
        ))}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    height: 200,
    marginBottom: Spacing.lg,
  },
  slide: {
    width,
    height: 200,
  },
  slideContent: {
    flex: 1,
    borderRadius: BorderRadius.xl,
    overflow: 'hidden',
    marginHorizontal: Spacing.lg,
    ...Shadows.lg,
  },
  slideImage: {
    width: '100%',
    height: '100%',
  },
  slideOverlay: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: '60%',
    justifyContent: 'flex-end',
    paddingHorizontal: Spacing.lg,
    paddingBottom: Spacing.lg,
  },
  slideTextContainer: {
    alignItems: 'flex-start',
  },
  slideTitle: {
    fontSize: Typography.fontSize['2xl'],
    fontWeight: Typography.fontWeight.bold,
    color: Colors.text.inverse,
    marginBottom: Spacing.xs,
    textShadowColor: 'rgba(0,0,0,0.5)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  slidePrice: {
    fontSize: Typography.fontSize.xl,
    fontWeight: Typography.fontWeight.semibold,
    color: Colors.accent.gold,
    marginBottom: Spacing.sm,
    textShadowColor: 'rgba(0,0,0,0.5)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  slideBadges: {
    flexDirection: 'row',
    gap: Spacing.sm,
  },
  slideBadge: {
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.xs,
    borderRadius: BorderRadius.lg,
  },
  featuredBadge: {
    backgroundColor: Colors.accent.gold,
  },
  promotionalBadge: {
    backgroundColor: Colors.primary[600],
  },
  slideBadgeText: {
    fontSize: Typography.fontSize.xs,
    fontWeight: Typography.fontWeight.bold,
    color: Colors.text.inverse,
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  pagination: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: Spacing.md,
    gap: Spacing.sm,
  },
  paginationDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: Colors.neutral[300],
  },
  paginationDotActive: {
    backgroundColor: Colors.primary[600],
    width: 24,
  },
});
