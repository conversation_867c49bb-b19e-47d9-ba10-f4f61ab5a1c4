import React, { useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { NavigationProps, MenuItem, CartItem } from '../../types';
import { Colors, Typography, Spacing, Shadows, BorderRadius } from '../../constants/theme';

const { width } = Dimensions.get('window');

interface ItemDetailScreenProps extends NavigationProps {
  route: {
    params: {
      item: MenuItem;
    };
  };
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9fafb',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#ffffff',
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#f3f4f6',
    alignItems: 'center',
    justifyContent: 'center',
  },
  backIcon: {
    fontSize: 18,
    fontWeight: '600',
  },
  favoriteButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#f3f4f6',
    alignItems: 'center',
    justifyContent: 'center',
  },
  heartIcon: {
    fontSize: 20,
  },
  itemSection: {
    backgroundColor: '#ffffff',
    padding: 24,
    alignItems: 'center',
  },
  itemImageContainer: {
    width: 120,
    height: 120,
    backgroundColor: '#f3f4f6',
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 16,
    position: 'relative',
  },
  itemEmoji: {
    fontSize: 48,
  },
  badgeContainer: {
    position: 'absolute',
    top: 8,
    right: 8,
    flexDirection: 'row',
    gap: 4,
  },
  vegBadge: {
    backgroundColor: '#22c55e',
    borderRadius: 8,
    paddingHorizontal: 6,
    paddingVertical: 2,
  },
  veganBadge: {
    backgroundColor: '#16a34a',
    borderRadius: 8,
    paddingHorizontal: 6,
    paddingVertical: 2,
  },
  gfBadge: {
    backgroundColor: '#f59e0b',
    borderRadius: 8,
    paddingHorizontal: 6,
    paddingVertical: 2,
  },
  badgeText: {
    fontSize: 10,
    fontWeight: '600',
    color: '#ffffff',
  },
  itemInfo: {
    alignItems: 'center',
  },
  itemName: {
    fontSize: 24,
    fontWeight: '700',
    color: '#111827',
    fontFamily: 'Lufga, system-ui, sans-serif',
    textAlign: 'center',
    marginBottom: 8,
  },
  itemDescription: {
    fontSize: 16,
    color: '#6b7280',
    fontFamily: 'Lufga, system-ui, sans-serif',
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 16,
  },
  itemMetrics: {
    flexDirection: 'row',
    gap: 24,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  starIcon: {
    fontSize: 16,
    color: '#fbbf24',
  },
  ratingText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#111827',
    fontFamily: 'Lufga, system-ui, sans-serif',
  },
  reviewCount: {
    fontSize: 14,
    color: '#6b7280',
    fontFamily: 'Lufga, system-ui, sans-serif',
  },
  prepTimeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  clockIcon: {
    fontSize: 16,
  },
  prepTimeText: {
    fontSize: 14,
    color: '#6b7280',
    fontFamily: 'Lufga, system-ui, sans-serif',
  },
  section: {
    backgroundColor: '#ffffff',
    marginTop: 8,
    padding: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
    fontFamily: 'Lufga, system-ui, sans-serif',
    marginBottom: 12,
  },
  ingredientsList: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  ingredientTag: {
    backgroundColor: '#f0fdf4',
    borderWidth: 1,
    borderColor: '#bbf7d0',
    borderRadius: 16,
    paddingHorizontal: 12,
    paddingVertical: 6,
  },
  ingredientText: {
    fontSize: 14,
    color: '#166534',
    fontFamily: 'Lufga, system-ui, sans-serif',
  },
  allergensList: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  allergenTag: {
    backgroundColor: '#fef3c7',
    borderWidth: 1,
    borderColor: '#fde68a',
    borderRadius: 16,
    paddingHorizontal: 12,
    paddingVertical: 6,
  },
  allergenText: {
    fontSize: 14,
    color: '#92400e',
    fontFamily: 'Lufga, system-ui, sans-serif',
  },
});

export default function ItemDetailScreen({ navigation, route }: ItemDetailScreenProps) {
  const { item } = route.params;
  const [quantity, setQuantity] = useState(1);
  const [selectedCustomizations, setSelectedCustomizations] = useState<{ [key: string]: string[] }>({});
  const [specialInstructions, setSpecialInstructions] = useState('');
  const [isFavorite, setIsFavorite] = useState(false);

  const calculatePrice = () => {
    let basePrice = item.price;
    let customizationPrice = 0;

    if (item.customizations) {
      item.customizations.forEach(customization => {
        const selectedOptions = selectedCustomizations[customization.id] || [];
        selectedOptions.forEach(optionId => {
          const option = customization.options.find(opt => opt.id === optionId);
          if (option) {
            customizationPrice += option.price;
          }
        });
      });
    }

    return (basePrice + customizationPrice) * quantity;
  };

  const handleCustomizationSelect = (customizationId: string, optionId: string) => {
    const customization = item.customizations?.find(c => c.id === customizationId);
    if (!customization) return;

    setSelectedCustomizations(prev => {
      const current = prev[customizationId] || [];
      
      if (customization.maxSelections === 1) {
        // Single selection
        return { ...prev, [customizationId]: [optionId] };
      } else {
        // Multiple selections
        const isSelected = current.includes(optionId);
        if (isSelected) {
          return { ...prev, [customizationId]: current.filter(id => id !== optionId) };
        } else {
          const maxSelections = customization.maxSelections || Infinity;
          if (current.length < maxSelections) {
            return { ...prev, [customizationId]: [...current, optionId] };
          }
          return prev;
        }
      }
    });
  };

  const addToCart = () => {
    const cartItem: CartItem = {
      id: `${item.id}-${Date.now()}`,
      menuItem: item,
      quantity,
      customizations: selectedCustomizations,
      totalPrice: calculatePrice(),
      specialInstructions: specialInstructions || undefined,
    };

    // Here you would typically dispatch to a cart context or state management
    console.log('Adding to cart:', cartItem);
    navigation.goBack();
  };

  const isCustomizationSelected = (customizationId: string, optionId: string) => {
    return selectedCustomizations[customizationId]?.includes(optionId) || false;
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          onPress={() => navigation.goBack()}
          style={styles.backButton}
        >
          <Ionicons name="arrow-back" size={24} color="#111827" />
        </TouchableOpacity>
        <TouchableOpacity
          onPress={() => setIsFavorite(!isFavorite)}
          style={styles.favoriteButton}
        >
          <Ionicons
            name={isFavorite ? "heart" : "heart-outline"}
            size={24}
            color={isFavorite ? "#dc2626" : "#9ca3af"}
          />
        </TouchableOpacity>
      </View>

      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Item Image & Info */}
        <View style={styles.itemSection}>
          <View style={styles.itemImageContainer}>
            <Text style={styles.itemEmoji}>{item.image}</Text>
            <View style={styles.badgeContainer}>
              {item.isVegetarian && (
                <View style={styles.vegBadge}>
                  <Text style={styles.badgeText}>V</Text>
                </View>
              )}
              {item.isVegan && (
                <View style={styles.veganBadge}>
                  <Text style={styles.badgeText}>VG</Text>
                </View>
              )}
              {item.isGlutenFree && (
                <View style={styles.gfBadge}>
                  <Text style={styles.badgeText}>GF</Text>
                </View>
              )}
            </View>
          </View>
          
          <View style={styles.itemInfo}>
            <Text style={styles.itemName}>{item.name}</Text>
            <Text style={styles.itemDescription}>{item.description}</Text>
            
            <View style={styles.itemMetrics}>
              <View style={styles.ratingContainer}>
                <Ionicons name="star" size={16} color="#fbbf24" />
                <Text style={styles.ratingText}>{item.rating}</Text>
                <Text style={styles.reviewCount}>(2.1k reviews)</Text>
              </View>
              <View style={styles.prepTimeContainer}>
                <Ionicons name="time" size={16} color="#6b7280" />
                <Text style={styles.prepTimeText}>{item.prepTime}</Text>
              </View>
            </View>
          </View>
        </View>

        {/* Ingredients */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Ingredients</Text>
          <View style={styles.ingredientsList}>
            {item.ingredients.map((ingredient, index) => (
              <View key={index} style={styles.ingredientTag}>
                <Text style={styles.ingredientText}>{ingredient}</Text>
              </View>
            ))}
          </View>
        </View>

        {/* Allergens */}
        {item.allergens.length > 0 && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Allergens</Text>
            <View style={styles.allergensList}>
              {item.allergens.map((allergen, index) => (
                <View key={index} style={styles.allergenTag}>
                  <Text style={styles.allergenText}>⚠️ {allergen}</Text>
                </View>
              ))}
            </View>
          </View>
        )}

        {/* Customizations */}
        {item.customizations && item.customizations.map((customization) => (
          <View key={customization.id} style={styles.section}>
            <View style={styles.customizationHeader}>
              <Text style={styles.sectionTitle}>{customization.name}</Text>
              {customization.required && (
                <View style={styles.requiredBadge}>
                  <Text style={styles.requiredText}>Required</Text>
                </View>
              )}
            </View>
            
            <View style={styles.optionsList}>
              {customization.options.map((option) => (
                <TouchableOpacity
                  key={option.id}
                  style={[
                    styles.optionButton,
                    isCustomizationSelected(customization.id, option.id) && styles.selectedOption
                  ]}
                  onPress={() => handleCustomizationSelect(customization.id, option.id)}
                >
                  <View style={styles.optionInfo}>
                    <Text style={[
                      styles.optionName,
                      isCustomizationSelected(customization.id, option.id) && styles.selectedOptionText
                    ]}>
                      {option.name}
                    </Text>
                    {option.price > 0 && (
                      <Text style={[
                        styles.optionPrice,
                        isCustomizationSelected(customization.id, option.id) && styles.selectedOptionText
                      ]}>
                        +R{option.price}
                      </Text>
                    )}
                  </View>
                  <View style={[
                    styles.radioButton,
                    isCustomizationSelected(customization.id, option.id) && styles.selectedRadio
                  ]}>
                    {isCustomizationSelected(customization.id, option.id) && (
                      <View style={styles.radioInner} />
                    )}
                  </View>
                </TouchableOpacity>
              ))}
            </View>
          </View>
        ))}

        {/* Special Instructions */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Special Instructions</Text>
          <View style={styles.instructionsContainer}>
            <Text style={styles.instructionsPlaceholder}>
              Any special requests? (e.g., extra crispy, light sauce)
            </Text>
          </View>
        </View>

        {/* Bottom Padding */}
        <View style={styles.bottomPadding} />
      </ScrollView>

      {/* Bottom Bar */}
      <View style={styles.bottomBar}>
        <View style={styles.quantityContainer}>
          <TouchableOpacity 
            onPress={() => setQuantity(Math.max(1, quantity - 1))}
            style={styles.quantityButton}
          >
            <Text style={styles.quantityButtonText}>−</Text>
          </TouchableOpacity>
          <Text style={styles.quantityText}>{quantity}</Text>
          <TouchableOpacity 
            onPress={() => setQuantity(quantity + 1)}
            style={styles.quantityButton}
          >
            <Text style={styles.quantityButtonText}>+</Text>
          </TouchableOpacity>
        </View>
        
        <LinearGradient
          colors={['#dc2626', '#b91c1c']}
          style={styles.addToCartButton}
        >
          <TouchableOpacity style={styles.addToCartButtonInner} onPress={addToCart}>
            <Text style={styles.addToCartText}>
              Add to Cart • R{calculatePrice()}
            </Text>
          </TouchableOpacity>
        </LinearGradient>
      </View>
    </SafeAreaView>
  );
}

const additionalStyles = StyleSheet.create({
  customizationHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  requiredBadge: {
    backgroundColor: '#dc2626',
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  requiredText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#ffffff',
  },
  optionsList: {
    gap: 8,
  },
  optionButton: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#f9fafb',
    borderRadius: 12,
    borderWidth: 2,
    borderColor: '#e5e7eb',
  },
  selectedOption: {
    backgroundColor: '#fef2f2',
    borderColor: '#dc2626',
  },
  optionInfo: {
    flex: 1,
  },
  optionName: {
    fontSize: 16,
    fontWeight: '500',
    color: '#111827',
    fontFamily: 'Lufga, system-ui, sans-serif',
    marginBottom: 2,
  },
  selectedOptionText: {
    color: '#dc2626',
    fontWeight: '600',
  },
  optionPrice: {
    fontSize: 14,
    color: '#6b7280',
    fontFamily: 'Lufga, system-ui, sans-serif',
  },
  radioButton: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: '#d1d5db',
    alignItems: 'center',
    justifyContent: 'center',
  },
  selectedRadio: {
    borderColor: '#dc2626',
  },
  radioInner: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: '#dc2626',
  },
  instructionsContainer: {
    backgroundColor: '#f9fafb',
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: '#e5e7eb',
  },
  instructionsPlaceholder: {
    fontSize: 14,
    color: '#9ca3af',
    fontFamily: 'Lufga, system-ui, sans-serif',
  },
  bottomPadding: {
    height: 100,
  },
  bottomBar: {
    backgroundColor: '#ffffff',
    paddingHorizontal: 20,
    paddingVertical: 16,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
    borderTopWidth: 1,
    borderTopColor: '#e5e7eb',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 8,
  },
  quantityContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f3f4f6',
    borderRadius: 12,
    padding: 4,
  },
  quantityButton: {
    width: 36,
    height: 36,
    borderRadius: 8,
    backgroundColor: '#ffffff',
    alignItems: 'center',
    justifyContent: 'center',
  },
  quantityButtonText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#374151',
  },
  quantityText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
    fontFamily: 'Lufga, system-ui, sans-serif',
    marginHorizontal: 16,
    minWidth: 24,
    textAlign: 'center',
  },
  addToCartButton: {
    flex: 1,
    borderRadius: BorderRadius.lg,
    ...Shadows.lg,
  },
  addToCartButtonInner: {
    paddingVertical: Spacing.lg,
    alignItems: 'center',
    justifyContent: 'center',
  },
  addToCartText: {
    fontSize: Typography.fontSize.base,
    fontWeight: Typography.fontWeight.semibold,
    color: Colors.text.inverse,
    letterSpacing: 0.5,
  },
});

// Merge styles
Object.assign(styles, additionalStyles);
