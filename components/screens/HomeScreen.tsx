import React, { useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  TextInput,
  StyleSheet,
  Dimensions,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { NavigationProps } from '../../types';
import { mockMenuItems, categories } from '../../utils/mockData';
import { Colors, Typography, Spacing, Shadows, BorderRadius } from '../../constants/theme';
import FoodSlideshow from '../FoodSlideshow';
import EnhancedFoodCard from '../EnhancedFoodCard';
import { searchMenuItems, debounce } from '../../utils/searchUtils';

const { width } = Dimensions.get('window');

interface HomeScreenProps extends NavigationProps {}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9fafb',
  },
  statusBar: {
    backgroundColor: '#ffffff',
    paddingHorizontal: 16,
    paddingVertical: 8,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  timeText: {
    fontSize: 14,
    fontWeight: '600',
    fontFamily: 'Lufga, system-ui, sans-serif',
  },
  batteryContainer: {
    flexDirection: 'row',
    gap: 4,
  },
  batteryBar: {
    width: 16,
    height: 8,
    borderRadius: 2,
  },
  header: {
    backgroundColor: '#ffffff',
    paddingHorizontal: 24,
    paddingVertical: 16,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  locationContainer: {
    flex: 1,
  },
  locationRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  locationIcon: {
    marginRight: 8,
  },
  deliveringText: {
    fontSize: 14,
    color: '#6b7280',
    fontFamily: 'Lufga, system-ui, sans-serif',
  },
  addressText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
    fontFamily: 'Lufga, system-ui, sans-serif',
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.md,
  },
  cartButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#f3f4f6',
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
  },
  cartBadge: {
    position: 'absolute',
    top: -4,
    right: -4,
    backgroundColor: Colors.primary[600],
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 4,
  },
  cartBadgeText: {
    fontSize: 12,
    fontWeight: 'bold',
    color: Colors.text.inverse,
  },
  profileButton: {
    width: 40,
    height: 40,
    backgroundColor: '#e5e7eb',
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },

  searchContainer: {
    backgroundColor: '#ffffff',
    paddingHorizontal: 24,
    paddingBottom: 16,
  },
  searchBar: {
    backgroundColor: Colors.background.tertiary,
    borderRadius: BorderRadius.lg,
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.md,
    flexDirection: 'row',
    alignItems: 'center',
    ...Shadows.sm,
    borderWidth: 1,
    borderColor: Colors.neutral[200],
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: 14,
    fontFamily: 'Lufga, system-ui, sans-serif',
  },
  clearSearchButton: {
    padding: Spacing.xs,
    marginLeft: Spacing.sm,
  },
  brandSection: {
    backgroundColor: '#dc2626',
    paddingHorizontal: 24,
    paddingVertical: 32,
    position: 'relative',
    overflow: 'hidden',
  },
  backgroundDecoration1: {
    position: 'absolute',
    top: 0,
    right: 0,
    width: 128,
    height: 128,
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    borderRadius: 64,
    transform: [{ translateX: 64 }, { translateY: -64 }],
  },
  backgroundDecoration2: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    width: 96,
    height: 96,
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    borderRadius: 48,
    transform: [{ translateX: -32 }, { translateY: 32 }],
  },
  brandContent: {
    position: 'relative',
    zIndex: 10,
  },
  logoContainer: {
    marginBottom: 24,
  },
  logoRow: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    justifyContent: 'space-between',
    maxWidth: 300,
  },
  leSection: {
    alignItems: 'flex-start',
  },
  leText: {
    color: '#ffffff',
    fontSize: 18,
    fontWeight: '900',
    fontFamily: 'Lufga, system-ui, sans-serif',
    letterSpacing: 1.2,
  },
  greenLine: {
    width: 32,
    height: 4,
    backgroundColor: '#22c55e',
    borderRadius: 2,
    marginTop: 4,
  },
  pizzeriaText: {
    color: '#ffffff',
    fontSize: 30,
    fontWeight: '900',
    fontFamily: 'Lufga, system-ui, sans-serif',
    letterSpacing: -0.5,
    lineHeight: 30,
    marginHorizontal: 8,
  },
  numberSection: {
    alignItems: 'flex-end',
  },
  numberText: {
    color: '#ffffff',
    fontSize: 18,
    fontWeight: '900',
    fontFamily: 'Lufga, system-ui, sans-serif',
  },
  redLine: {
    width: 32,
    height: 4,
    backgroundColor: '#ef4444',
    borderRadius: 2,
    marginTop: 4,
  },
  tagline: {
    color: '#fecaca',
    fontSize: 14,
    fontFamily: 'Lufga, system-ui, sans-serif',
    marginBottom: 16,
  },
  infoRow: {
    flexDirection: 'row',
    gap: 16,
  },
  infoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  infoIcon: {
    fontSize: 16,
  },
  starIcon: {
    fontSize: 16,
    color: '#fbbf24',
  },
  infoText: {
    color: '#ffffff',
    fontSize: 14,
    fontFamily: 'Lufga, system-ui, sans-serif',
  },
});

export default function HomeScreen({ navigation }: HomeScreenProps) {
  const [activeCategory, setActiveCategory] = useState('popular');
  const [favorites, setFavorites] = useState(new Set(['1', '3']));
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredItems, setFilteredItems] = useState(mockMenuItems);
  const [isSearching, setIsSearching] = useState(false);

  const toggleFavorite = (id: string) => {
    const newFavorites = new Set(favorites);
    if (newFavorites.has(id)) {
      newFavorites.delete(id);
    } else {
      newFavorites.add(id);
    }
    setFavorites(newFavorites);
  };

  // Debounced search function
  const debouncedSearch = React.useCallback(
    debounce((query: string) => {
      setIsSearching(true);
      const results = searchMenuItems(mockMenuItems, query);
      setFilteredItems(results);
      setIsSearching(false);
    }, 300),
    []
  );

  // Handle search input
  const handleSearchChange = (text: string) => {
    setSearchQuery(text);
    if (text.trim()) {
      debouncedSearch(text);
    } else {
      setFilteredItems(mockMenuItems);
      setIsSearching(false);
    }
  };

  const featuredItems = mockMenuItems.filter(item => item.isFeatured || item.isPromotional).slice(0, 5);
  const displayItems = mockMenuItems.slice(0, 6);

  const LePizzeriaLogo = () => (
    <View style={styles.logoContainer}>
      <View style={styles.logoRow}>
        <View style={styles.leSection}>
          <Text style={styles.leText}>LE</Text>
          <View style={styles.greenLine} />
        </View>
        
        <Text style={styles.pizzeriaText}>PIZZERIA</Text>
        
        <View style={styles.numberSection}>
          <Text style={styles.numberText}>24</Text>
          <View style={styles.redLine} />
        </View>
      </View>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* Status Bar */}
      <View style={styles.statusBar}>
        <Text style={styles.timeText}>9:41</Text>
        <View style={styles.batteryContainer}>
          <View style={[styles.batteryBar, { backgroundColor: '#000' }]} />
          <View style={[styles.batteryBar, { backgroundColor: '#000' }]} />
          <View style={[styles.batteryBar, { backgroundColor: '#22c55e' }]} />
        </View>
      </View>

      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.locationContainer}>
            <View style={styles.locationRow}>
              <Ionicons name="location" size={16} color="#6b7280" style={styles.locationIcon} />
              <Text style={styles.deliveringText}>Delivering to</Text>
            </View>
            <Text style={styles.addressText}>Observatory, Cape Town</Text>
          </View>
          <View style={styles.headerActions}>
            <TouchableOpacity
              style={styles.cartButton}
              onPress={() => navigation.navigate('Cart')}
            >
              <Ionicons name="bag" size={20} color="#374151" />
              {/* Cart badge - you can connect this to actual cart count */}
              <View style={styles.cartBadge}>
                <Text style={styles.cartBadgeText}>2</Text>
              </View>
            </TouchableOpacity>
            <TouchableOpacity style={styles.profileButton}>
              <Ionicons name="person" size={18} color="#374151" />
            </TouchableOpacity>
          </View>
        </View>

        {/* Search Bar */}
        <View style={styles.searchContainer}>
          <View style={styles.searchBar}>
            <Ionicons name="search" size={20} color="#9ca3af" style={styles.searchIcon} />
            <TextInput
              style={styles.searchInput}
              placeholder="Search for pizza, pasta..."
              value={searchQuery}
              onChangeText={handleSearchChange}
              placeholderTextColor="#9ca3af"
            />
            {searchQuery.length > 0 && (
              <TouchableOpacity
                onPress={() => {
                  setSearchQuery('');
                  setFilteredItems(mockMenuItems);
                  setIsSearching(false);
                }}
                style={styles.clearSearchButton}
              >
                <Ionicons name="close" size={20} color="#9ca3af" />
              </TouchableOpacity>
            )}
          </View>
        </View>

        {/* Featured Food Slideshow */}
        <FoodSlideshow
          items={featuredItems}
          onItemPress={(item) => navigation.navigate('ItemDetail', { item })}
        />

        {/* Categories */}
        <View style={styles.categoriesSection}>
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            <View style={styles.categoriesContainer}>
              {categories.map((category) => (
                <TouchableOpacity
                  key={category.id}
                  onPress={() => setActiveCategory(category.id)}
                  style={[
                    styles.categoryButton,
                    activeCategory === category.id && styles.activeCategoryButton
                  ]}
                >
                  <Text style={[
                    styles.categoryText,
                    activeCategory === category.id && styles.activeCategoryText
                  ]}>
                    {category.name}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </ScrollView>
        </View>

        {/* Featured Items */}
        <View style={styles.featuredSection}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Popular Menu</Text>
            <TouchableOpacity
              onPress={() => navigation.navigate('Menu')}
              style={styles.seeAllButton}
            >
              <Text style={styles.seeAllText}>See all</Text>
              <Ionicons name="chevron-forward" size={16} color="#dc2626" />
            </TouchableOpacity>
          </View>

          <View style={styles.foodGrid}>
            {displayItems.map((item) => (
              <EnhancedFoodCard
                key={item.id}
                item={item}
                onPress={() => navigation.navigate('ItemDetail', { item })}
                onFavoritePress={() => toggleFavorite(item.id)}
                isFavorite={favorites.has(item.id)}
              />
            ))}
          </View>
        </View>

        {/* Quick Actions */}
        <View style={styles.quickActionsSection}>
          <View style={styles.quickActionsGrid}>
            <TouchableOpacity style={styles.quickActionButton}>
              <Ionicons name="flash" size={24} color="#dc2626" style={styles.quickActionIcon} />
              <Text style={styles.quickActionTitle}>Quick Order</Text>
              <Text style={styles.quickActionSubtitle}>Reorder favorites</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.quickActionButton, styles.trackOrderButton]}
              onPress={() => navigation.navigate('OrderTracking')}
            >
              <Ionicons name="location" size={24} color="#dc2626" style={styles.quickActionIcon} />
              <Text style={styles.quickActionTitle}>Track Order</Text>
              <Text style={styles.quickActionSubtitle}>Live delivery status</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Bottom Padding */}
        <View style={styles.bottomPadding} />
      </ScrollView>
    </SafeAreaView>
  );
}

const additionalStyles = StyleSheet.create({
  categoriesSection: {
    backgroundColor: '#ffffff',
    paddingHorizontal: 16,
    paddingVertical: 16,
  },
  categoriesContainer: {
    flexDirection: 'row',
    gap: 8,
  },
  categoryButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: '#f3f4f6',
  },
  activeCategoryButton: {
    backgroundColor: '#dc2626',
    shadowColor: '#dc2626',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 4,
  },
  categoryText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#6b7280',
    fontFamily: 'Lufga, system-ui, sans-serif',
  },
  activeCategoryText: {
    color: '#ffffff',
    fontWeight: '600',
  },
  featuredSection: {
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.lg,
  },
  foodGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#111827',
    fontFamily: 'Lufga, system-ui, sans-serif',
  },
  seeAllButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  seeAllText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#dc2626',
    fontFamily: 'Lufga, system-ui, sans-serif',
  },
  chevronIcon: {
    fontSize: 16,
    color: '#dc2626',
    fontWeight: '600',
  },
  itemsList: {
    gap: 16,
  },
  itemCard: {
    backgroundColor: Colors.background.primary,
    borderRadius: BorderRadius.xl,
    padding: Spacing.lg,
    ...Shadows.md,
    borderWidth: 1,
    borderColor: Colors.neutral[100],
    marginBottom: Spacing.sm,
  },
  itemContent: {
    flexDirection: 'row',
    gap: 16,
  },
  itemImage: {
    width: 80,
    height: 80,
    backgroundColor: '#f3f4f6',
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  itemEmoji: {
    fontSize: 32,
  },
  itemDetails: {
    flex: 1,
  },
  itemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 4,
  },
  itemName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
    fontFamily: 'Lufga, system-ui, sans-serif',
    flex: 1,
  },
  favoriteButton: {
    padding: 4,
  },
  heartIcon: {
    fontSize: 20,
  },
  favoriteHeart: {
    color: '#dc2626',
  },
  itemDescription: {
    fontSize: 14,
    color: '#6b7280',
    fontFamily: 'Lufga, system-ui, sans-serif',
    lineHeight: 20,
    marginBottom: 8,
  },
  itemFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  itemInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  itemPrice: {
    fontSize: 18,
    fontWeight: '700',
    color: '#111827',
    fontFamily: 'Lufga, system-ui, sans-serif',
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 2,
  },
  ratingText: {
    fontSize: 12,
    color: '#6b7280',
    fontFamily: 'Lufga, system-ui, sans-serif',
  },
  addButton: {
    backgroundColor: '#dc2626',
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  addIcon: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
  },
  quickActionsSection: {
    paddingHorizontal: 24,
    paddingVertical: 16,
  },
  quickActionsGrid: {
    flexDirection: 'row',
    gap: 12,
  },
  quickActionButton: {
    flex: 1,
    backgroundColor: Colors.background.primary,
    borderWidth: 1,
    borderColor: Colors.neutral[200],
    borderRadius: BorderRadius.xl,
    padding: Spacing.xl,
    alignItems: 'center',
    ...Shadows.md,
  },
  trackOrderButton: {
    backgroundColor: '#dbeafe',
    borderColor: '#bfdbfe',
  },
  quickActionIcon: {
    fontSize: 24,
    marginBottom: 8,
  },
  quickActionTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#166534',
    fontFamily: 'Lufga, system-ui, sans-serif',
    marginBottom: 4,
  },
  quickActionSubtitle: {
    fontSize: 12,
    color: '#16a34a',
    fontFamily: 'Lufga, system-ui, sans-serif',
    textAlign: 'center',
  },
  bottomPadding: {
    height: 80,
  },
});

// Merge styles
Object.assign(styles, additionalStyles);
