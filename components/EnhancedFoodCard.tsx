import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Image,
  Dimensions,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { MenuItem } from '../types';
import { Colors, Typography, Spacing, Shadows, BorderRadius } from '../constants/theme';

const { width } = Dimensions.get('window');
const cardWidth = (width - Spacing['3xl']) / 2;

interface EnhancedFoodCardProps {
  item: MenuItem;
  onPress: () => void;
  onFavoritePress?: () => void;
  isFavorite?: boolean;
}

export default function EnhancedFoodCard({
  item,
  onPress,
  onFavoritePress,
  isFavorite = false,
}: EnhancedFoodCardProps) {
  return (
    <TouchableOpacity style={styles.container} onPress={onPress}>
      <View style={styles.card}>
        {/* Food Image - 70% of card height */}
        <View style={styles.imageContainer}>
          <Image
            source={item.imageUri || { uri: item.image }}
            style={styles.foodImage}
            resizeMode="cover"
          />
          
          {/* Favorite Button */}
          {onFavoritePress && (
            <TouchableOpacity
              style={styles.favoriteButton}
              onPress={onFavoritePress}
            >
              <Ionicons
                name={isFavorite ? "heart" : "heart-outline"}
                size={20}
                color={isFavorite ? Colors.primary[600] : Colors.text.inverse}
              />
            </TouchableOpacity>
          )}

          {/* Featured/Promotional Badges */}
          {(item.isFeatured || item.isPromotional) && (
            <View style={styles.badgeContainer}>
              {item.isFeatured && (
                <View style={[styles.badge, styles.featuredBadge]}>
                  <Text style={styles.badgeText}>Featured</Text>
                </View>
              )}
              {item.isPromotional && (
                <View style={[styles.badge, styles.promotionalBadge]}>
                  <Text style={styles.badgeText}>Special</Text>
                </View>
              )}
            </View>
          )}
        </View>

        {/* Content Overlay - 30% of card height */}
        <LinearGradient
          colors={['transparent', 'rgba(0,0,0,0.7)', 'rgba(0,0,0,0.9)']}
          style={styles.contentOverlay}
        >
          <View style={styles.contentContainer}>
            <Text style={styles.itemName} numberOfLines={1}>
              {item.name}
            </Text>
            <Text style={styles.itemDescription} numberOfLines={2}>
              {item.description}
            </Text>
            <View style={styles.priceContainer}>
              <Text style={styles.itemPrice}>R{item.price}</Text>
              <TouchableOpacity style={styles.addButton}>
                <Ionicons name="add" size={16} color={Colors.text.inverse} />
              </TouchableOpacity>
            </View>
          </View>
        </LinearGradient>
      </View>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    width: cardWidth,
    marginBottom: Spacing.lg,
  },
  card: {
    height: 200, // Minimum 200px as requested
    borderRadius: BorderRadius.xl,
    overflow: 'hidden',
    backgroundColor: Colors.background.primary,
    ...Shadows.md,
  },
  imageContainer: {
    height: '70%', // 70% for image
    position: 'relative',
  },
  foodImage: {
    width: '100%',
    height: '100%',
  },
  favoriteButton: {
    position: 'absolute',
    top: Spacing.md,
    right: Spacing.md,
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: 'rgba(0,0,0,0.3)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  badgeContainer: {
    position: 'absolute',
    top: Spacing.md,
    left: Spacing.md,
    flexDirection: 'column',
    gap: Spacing.xs,
  },
  badge: {
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs,
    borderRadius: BorderRadius.md,
  },
  featuredBadge: {
    backgroundColor: Colors.accent.gold,
  },
  promotionalBadge: {
    backgroundColor: Colors.primary[600],
  },
  badgeText: {
    fontSize: Typography.fontSize.xs,
    fontWeight: Typography.fontWeight.bold,
    color: Colors.text.inverse,
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  contentOverlay: {
    height: '30%', // 30% for content
    justifyContent: 'flex-end',
    paddingHorizontal: Spacing.md,
    paddingBottom: Spacing.md,
  },
  contentContainer: {
    flex: 1,
    justifyContent: 'flex-end',
  },
  itemName: {
    fontSize: Typography.fontSize.base,
    fontWeight: Typography.fontWeight.bold,
    color: Colors.text.inverse,
    marginBottom: Spacing.xs,
  },
  itemDescription: {
    fontSize: Typography.fontSize.sm,
    color: 'rgba(255,255,255,0.8)',
    lineHeight: 18,
    marginBottom: Spacing.sm,
  },
  priceContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  itemPrice: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.bold,
    color: Colors.text.inverse,
  },
  addButton: {
    width: 28,
    height: 28,
    borderRadius: 14,
    backgroundColor: Colors.primary[600],
    alignItems: 'center',
    justifyContent: 'center',
    ...Shadows.sm,
  },
});
