import React from 'react';
import { View, Text, StyleSheet, Dimensions } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

const { height } = Dimensions.get('window');

export default function LoadingScreen() {
  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.mainContainer}>
        {/* Logo container optimized for mobile */}
        <View style={styles.logoContainer}>
          {/* Logo recreation matching your app design */}
          <View style={styles.logoContent}>
            <View style={styles.topRow}>
              {/* Green line on left */}
              <View style={styles.greenLine} />

              {/* LE text */}
              <Text style={styles.leText}>LE</Text>
            </View>

            {/* PIZZERIA text */}
            <Text style={styles.pizzeriaText}>PIZZERIA</Text>

            <View style={styles.bottomRow}>
              {/* Pink/red line on right */}
              <View style={styles.redLine} />

              {/* 24 text */}
              <Text style={styles.twentyFourText}>24</Text>
            </View>
          </View>
        </View>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#dc2626', // bg-red-600
  },
  mainContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 16, // px-4
  },
  logoContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  logoContent: {
    alignItems: 'center',
  },
  topRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 4, // mb-1
    gap: 12, // space-x-3
  },
  greenLine: {
    width: 2, // w-0.5
    height: 32, // h-8
    backgroundColor: '#4ade80', // bg-green-400
    borderRadius: 1,
  },
  leText: {
    fontSize: 24, // text-2xl
    fontWeight: 'bold',
    color: '#ffffff',
  },
  pizzeriaText: {
    fontSize: 30, // text-3xl
    fontWeight: 'bold',
    color: '#ffffff',
    marginBottom: 4, // mb-1
    textAlign: 'center',
  },
  bottomRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 12, // space-x-3
  },
  redLine: {
    width: 2, // w-0.5
    height: 32, // h-8
    backgroundColor: '#fca5a5', // bg-red-300
    borderRadius: 1,
    opacity: 0.6,
  },
  twentyFourText: {
    fontSize: 24, // text-2xl
    fontWeight: 'bold',
    color: '#ffffff',
  },
});