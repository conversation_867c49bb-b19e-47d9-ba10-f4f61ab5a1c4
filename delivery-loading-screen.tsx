import React, { useEffect, useRef } from 'react';
import { View, Text, StyleSheet, Dimensions, Animated } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import { Colors, Typography, Spacing, Shadows, BorderRadius } from './constants/theme';

const { height } = Dimensions.get('window');

export default function LoadingScreen() {
  const scaleAnim = useRef(new Animated.Value(0.8)).current;
  const opacityAnim = useRef(new Animated.Value(0)).current;
  const pulseAnim = useRef(new Animated.Value(1)).current;

  useEffect(() => {
    // Initial entrance animation
    Animated.parallel([
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(opacityAnim, {
        toValue: 1,
        duration: 600,
        useNativeDriver: true,
      }),
    ]).start();

    // Continuous pulse animation
    const pulseAnimation = Animated.loop(
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 1.05,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
      ])
    );
    pulseAnimation.start();

    return () => pulseAnimation.stop();
  }, []);

  return (
    <LinearGradient
      colors={['#dc2626', '#b91c1c', '#991b1b']}
      style={styles.container}
    >
      <SafeAreaView style={styles.safeArea}>
        <View style={styles.mainContainer}>
          {/* Logo container with animations */}
          <Animated.View
            style={[
              styles.logoContainer,
              {
                opacity: opacityAnim,
                transform: [
                  { scale: scaleAnim },
                  { scale: pulseAnim }
                ]
              }
            ]}
          >
            {/* Logo recreation with luxury styling */}
            <View style={styles.logoContent}>
              <View style={styles.topRow}>
                {/* Green line on left */}
                <View style={styles.greenLine} />
                {/* LE text */}
                <Text style={styles.leText}>LE</Text>
              </View>

              {/* PIZZERIA text */}
              <Text style={styles.pizzeriaText}>PIZZERIA</Text>

              <View style={styles.bottomRow}>
                {/* Pink/red line on right */}
                <View style={styles.redLine} />
                {/* 24 text */}
                <Text style={styles.twentyFourText}>24</Text>
              </View>
            </View>
          </Animated.View>

          {/* Luxury tagline */}
          <Animated.View style={[styles.taglineContainer, { opacity: opacityAnim }]}>
            <Text style={styles.tagline}>Authentic Italian Excellence</Text>
            <Text style={styles.subtitle}>Crafted with passion, delivered with care</Text>
          </Animated.View>
        </View>
      </SafeAreaView>
    </LinearGradient>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  safeArea: {
    flex: 1,
  },
  mainContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: Spacing['2xl'],
  },
  logoContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: BorderRadius['3xl'],
    paddingHorizontal: Spacing['4xl'],
    paddingVertical: Spacing['3xl'],
    ...Shadows.xl,
    shadowColor: 'rgba(0, 0, 0, 0.3)',
  },
  logoContent: {
    alignItems: 'center',
  },
  topRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: Spacing.sm,
    gap: Spacing.lg,
  },
  greenLine: {
    width: 3,
    height: 40,
    backgroundColor: Colors.accent.green,
    borderRadius: BorderRadius.sm,
    ...Shadows.sm,
  },
  leText: {
    fontSize: Typography.fontSize['3xl'],
    fontWeight: Typography.fontWeight.extrabold,
    color: Colors.text.inverse,
    letterSpacing: 2,
  },
  pizzeriaText: {
    fontSize: Typography.fontSize['4xl'],
    fontWeight: Typography.fontWeight.extrabold,
    color: Colors.text.inverse,
    marginBottom: Spacing.sm,
    textAlign: 'center',
    letterSpacing: 3,
  },
  bottomRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: Spacing.lg,
  },
  redLine: {
    width: 3,
    height: 40,
    backgroundColor: Colors.primary[300],
    borderRadius: BorderRadius.sm,
    opacity: 0.8,
    ...Shadows.sm,
  },
  twentyFourText: {
    fontSize: Typography.fontSize['3xl'],
    fontWeight: Typography.fontWeight.extrabold,
    color: Colors.text.inverse,
    letterSpacing: 2,
  },
  taglineContainer: {
    marginTop: Spacing['4xl'],
    alignItems: 'center',
  },
  tagline: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.semibold,
    color: Colors.text.inverse,
    textAlign: 'center',
    marginBottom: Spacing.sm,
    letterSpacing: 1,
  },
  subtitle: {
    fontSize: Typography.fontSize.sm,
    fontWeight: Typography.fontWeight.normal,
    color: 'rgba(255, 255, 255, 0.8)',
    textAlign: 'center',
    letterSpacing: 0.5,
  },
});