// Premium Font Configuration for LePizzeria App

export const FontConfig = {
  // For iOS and Android system fonts
  regular: {
    fontFamily: 'System',
    fontWeight: '400' as const,
  },
  medium: {
    fontFamily: 'System',
    fontWeight: '500' as const,
  },
  semibold: {
    fontFamily: 'System',
    fontWeight: '600' as const,
  },
  bold: {
    fontFamily: 'System',
    fontWeight: '700' as const,
  },
  extrabold: {
    fontFamily: 'System',
    fontWeight: '800' as const,
  },
};

// Typography scale for consistent text sizing
export const TextStyles = {
  // Headings
  h1: {
    fontSize: 32,
    lineHeight: 40,
    ...FontConfig.extrabold,
    letterSpacing: -0.5,
  },
  h2: {
    fontSize: 28,
    lineHeight: 36,
    ...FontConfig.bold,
    letterSpacing: -0.3,
  },
  h3: {
    fontSize: 24,
    lineHeight: 32,
    ...FontConfig.bold,
    letterSpacing: -0.2,
  },
  h4: {
    fontSize: 20,
    lineHeight: 28,
    ...FontConfig.semibold,
  },
  h5: {
    fontSize: 18,
    lineHeight: 24,
    ...FontConfig.semibold,
  },
  h6: {
    fontSize: 16,
    lineHeight: 22,
    ...FontConfig.semibold,
  },
  
  // Body text
  bodyLarge: {
    fontSize: 18,
    lineHeight: 28,
    ...FontConfig.regular,
  },
  body: {
    fontSize: 16,
    lineHeight: 24,
    ...FontConfig.regular,
  },
  bodySmall: {
    fontSize: 14,
    lineHeight: 20,
    ...FontConfig.regular,
  },
  
  // Captions and labels
  caption: {
    fontSize: 12,
    lineHeight: 16,
    ...FontConfig.medium,
    letterSpacing: 0.4,
  },
  label: {
    fontSize: 14,
    lineHeight: 20,
    ...FontConfig.medium,
    letterSpacing: 0.1,
  },
  
  // Buttons
  button: {
    fontSize: 16,
    lineHeight: 24,
    ...FontConfig.semibold,
    letterSpacing: 0.5,
  },
  buttonSmall: {
    fontSize: 14,
    lineHeight: 20,
    ...FontConfig.semibold,
    letterSpacing: 0.3,
  },
  
  // Special text styles
  price: {
    fontSize: 18,
    lineHeight: 24,
    ...FontConfig.bold,
    letterSpacing: -0.1,
  },
  logo: {
    fontSize: 24,
    lineHeight: 32,
    ...FontConfig.extrabold,
    letterSpacing: 2,
  },
};
