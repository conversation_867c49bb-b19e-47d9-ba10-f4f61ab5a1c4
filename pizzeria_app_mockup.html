<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Le Pizzeria App Mockup</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #000;
            color: white;
            overflow-x: hidden;
        }

        .phone-container {
            width: 375px;
            height: 812px;
            margin: 20px auto;
            background: #000;
            border-radius: 40px;
            padding: 10px;
            box-shadow: 0 0 30px rgba(255, 255, 255, 0.1);
            position: relative;
        }

        .phone-screen {
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
            border-radius: 30px;
            overflow: hidden;
            position: relative;
        }

        .status-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 20px;
            font-size: 16px;
            font-weight: 600;
        }

        .status-right {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .battery {
            background: #34C759;
            color: #000;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }

        .app-content {
            padding: 0 20px;
            height: calc(100% - 150px);
            overflow-y: auto;
        }

        .tab-switcher {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-bottom: 30px;
        }

        .tab-btn {
            background: none;
            border: none;
            color: #888;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            padding: 10px 20px;
            border-radius: 20px;
            transition: all 0.3s ease;
        }

        .tab-btn.active {
            color: #fff;
            background: rgba(255, 255, 255, 0.1);
        }

        .page {
            display: none;
        }

        .page.active {
            display: block;
            animation: fadeIn 0.5s ease;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* Home Page Styles */
        .hero-section {
            background: linear-gradient(135deg, #FF6B35 0%, #F7931E 100%);
            border-radius: 20px;
            padding: 30px 25px;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
            animation: rotate 20s linear infinite;
        }

        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .hero-content {
            position: relative;
            z-index: 1;
        }

        .hero-title {
            font-size: 28px;
            font-weight: 800;
            margin-bottom: 10px;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
        }

        .hero-subtitle {
            font-size: 16px;
            opacity: 0.9;
            margin-bottom: 20px;
        }

        .cta-button {
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 12px 30px;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .cta-button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .section-title {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 20px;
            color: #fff;
        }

        .specials-grid {
            display: flex;
            flex-direction: column;
            gap: 15px;
            margin-bottom: 30px;
        }

        .special-card {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
        }

        .special-card:hover {
            transform: translateY(-5px);
            background: rgba(255, 255, 255, 0.08);
        }

        .special-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .special-name {
            font-size: 18px;
            font-weight: 600;
            color: #FF6B35;
        }

        .special-price {
            background: linear-gradient(45deg, #FF6B35, #F7931E);
            color: white;
            padding: 5px 12px;
            border-radius: 12px;
            font-size: 14px;
            font-weight: 700;
        }

        .special-description {
            color: #ccc;
            font-size: 14px;
            line-height: 1.4;
        }

        .popular-grid {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .popular-item {
            display: flex;
            align-items: center;
            background: rgba(255, 255, 255, 0.03);
            border-radius: 12px;
            padding: 15px;
            transition: all 0.3s ease;
        }

        .popular-item:hover {
            background: rgba(255, 255, 255, 0.06);
        }

        .popular-rank {
            background: #FF6B35;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            font-size: 14px;
            margin-right: 15px;
        }

        .popular-info h3 {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .popular-info p {
            color: #999;
            font-size: 12px;
        }

        /* Menu Page Styles */
        .category-section {
            margin-bottom: 35px;
        }

        .category-title {
            font-size: 22px;
            font-weight: 700;
            margin-bottom: 18px;
            color: #FF6B35;
            border-bottom: 2px solid rgba(255, 107, 53, 0.3);
            padding-bottom: 8px;
        }

        .menu-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }

        .menu-item {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            padding: 15px;
            text-align: center;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .menu-item:hover {
            transform: translateY(-8px);
            background: rgba(255, 255, 255, 0.08);
            box-shadow: 0 10px 30px rgba(255, 107, 53, 0.2);
        }

        .menu-item-image {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #FF6B35 0%, #F7931E 100%);
            border-radius: 50%;
            margin: 0 auto 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 30px;
        }

        .menu-item h3 {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 5px;
            color: #fff;
        }

        .menu-item-price {
            color: #FF6B35;
            font-weight: 700;
            font-size: 16px;
        }

        .bottom-nav {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(0, 0, 0, 0.9);
            backdrop-filter: blur(20px);
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            padding: 15px 0;
            display: flex;
            justify-content: space-around;
        }

        .nav-item {
            text-align: center;
            color: #666;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .nav-item.active {
            color: #FF6B35;
        }

        .nav-icon {
            font-size: 20px;
            margin-bottom: 5px;
        }

        .floating-cart {
            position: fixed;
            bottom: 100px;
            right: 30px;
            background: linear-gradient(45deg, #FF6B35, #F7931E);
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 10px 30px rgba(255, 107, 53, 0.4);
            cursor: pointer;
            transition: all 0.3s ease;
            z-index: 1000;
        }

        .floating-cart:hover {
            transform: scale(1.1);
        }

        .cart-badge {
            position: absolute;
            top: -5px;
            right: -5px;
            background: #fff;
            color: #FF6B35;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: 700;
        }
    </style>
</head>
<body>
    <div class="phone-container">
        <div class="phone-screen">
            <!-- Status Bar -->
            <div class="status-bar">
                <span>01:03 🌙</span>
                <div class="status-right">
                    <span>••••</span>
                    <span>📶</span>
                    <span>📶</span>
                    <span class="battery">85</span>
                </div>
            </div>

            <!-- Tab Switcher -->
            <div class="tab-switcher">
                <button class="tab-btn active" onclick="showPage('home')">Home</button>
                <button class="tab-btn" onclick="showPage('menu')">Menu</button>
            </div>

            <div class="app-content">
                <!-- HOME PAGE -->
                <div class="page active" id="home">
                    <!-- Hero Section -->
                    <div class="hero-section">
                        <div class="hero-content">
                            <h1 class="hero-title">Le Pizzeria</h1>
                            <p class="hero-subtitle">Authentic Italian flavors delivered to your door</p>
                            <button class="cta-button">Order Now</button>
                        </div>
                    </div>

                    <!-- Today's Specials -->
                    <section>
                        <h2 class="section-title">Today's Specials</h2>
                        <div class="specials-grid">
                            <div class="special-card">
                                <div class="special-header">
                                    <h3 class="special-name">Margherita Supreme</h3>
                                    <span class="special-price">€12.99</span>
                                </div>
                                <p class="special-description">Fresh mozzarella, San Marzano tomatoes, and basil on our signature sourdough crust</p>
                            </div>
                            
                            <div class="special-card">
                                <div class="special-header">
                                    <h3 class="special-name">Truffle Pasta Special</h3>
                                    <span class="special-price">€18.50</span>
                                </div>
                                <p class="special-description">Handmade fettuccine with black truffle, parmesan, and wild mushrooms</p>
                            </div>

                            <div class="special-card">
                                <div class="special-header">
                                    <h3 class="special-name">Family Combo</h3>
                                    <span class="special-price">€35.99</span>
                                </div>
                                <p class="special-description">2 Large pizzas + garlic bread + 1.5L beverage</p>
                            </div>
                        </div>
                    </section>

                    <!-- Popular Orders -->
                    <section>
                        <h2 class="section-title">Popular Orders</h2>
                        <div class="popular-grid">
                            <div class="popular-item">
                                <div class="popular-rank">1</div>
                                <div class="popular-info">
                                    <h3>Quattro Stagioni</h3>
                                    <p>Most ordered this week</p>
                                </div>
                            </div>
                            
                            <div class="popular-item">
                                <div class="popular-rank">2</div>
                                <div class="popular-info">
                                    <h3>Carbonara Classica</h3>
                                    <p>Chef's recommendation</p>
                                </div>
                            </div>
                            
                            <div class="popular-item">
                                <div class="popular-rank">3</div>
                                <div class="popular-info">
                                    <h3>Bruschetta Trio</h3>
                                    <p>Perfect starter</p>
                                </div>
                            </div>

                            <div class="popular-item">
                                <div class="popular-rank">4</div>
                                <div class="popular-info">
                                    <h3>Tiramisu</h3>
                                    <p>Authentic Italian dessert</p>
                                </div>
                            </div>
                        </div>
                    </section>
                </div>

                <!-- MENU PAGE -->
                <div class="page" id="menu">
                    <!-- Le Pizzeria Tapas -->
                    <div class="category-section">
                        <h2 class="category-title">Le Pizzeria Tapas</h2>
                        <div class="menu-grid">
                            <div class="menu-item">
                                <div class="menu-item-image">🥖</div>
                                <h3>Bruschetta Trio</h3>
                                <p class="menu-item-price">€8.50</p>
                            </div>
                            <div class="menu-item">
                                <div class="menu-item-image">🧄</div>
                                <h3>Garlic Bread</h3>
                                <p class="menu-item-price">€5.99</p>
                            </div>
                            <div class="menu-item">
                                <div class="menu-item-image">🫒</div>
                                <h3>Olive Medley</h3>
                                <p class="menu-item-price">€6.50</p>
                            </div>
                            <div class="menu-item">
                                <div class="menu-item-image">🧀</div>
                                <h3>Antipasti Board</h3>
                                <p class="menu-item-price">€14.99</p>
                            </div>
                        </div>
                    </div>

                    <!-- Le Pizzeria Pizzas -->
                    <div class="category-section">
                        <h2 class="category-title">Le Pizzeria Pizzas</h2>
                        <div class="menu-grid">
                            <div class="menu-item">
                                <div class="menu-item-image">🍕</div>
                                <h3>Margherita</h3>
                                <p class="menu-item-price">€11.99</p>
                            </div>
                            <div class="menu-item">
                                <div class="menu-item-image">🍄</div>
                                <h3>Quattro Stagioni</h3>
                                <p class="menu-item-price">€15.50</p>
                            </div>
                            <div class="menu-item">
                                <div class="menu-item-image">🥓</div>
                                <h3>Diavola</h3>
                                <p class="menu-item-price">€13.99</p>
                            </div>
                            <div class="menu-item">
                                <div class="menu-item-image">🌿</div>
                                <h3>Pesto Supremo</h3>
                                <p class="menu-item-price">€14.50</p>
                            </div>
                        </div>
                    </div>

                    <!-- Pastas -->
                    <div class="category-section">
                        <h2 class="category-title">Pastas</h2>
                        <div class="menu-grid">
                            <div class="menu-item">
                                <div class="menu-item-image">🍝</div>
                                <h3>Carbonara</h3>
                                <p class="menu-item-price">€12.99</p>
                            </div>
                            <div class="menu-item">
                                <div class="menu-item-image">🍅</div>
                                <h3>Arrabbiata</h3>
                                <p class="menu-item-price">€10.50</p>
                            </div>
                            <div class="menu-item">
                                <div class="menu-item-image">🥩</div>
                                <h3>Bolognese</h3>
                                <p class="menu-item-price">€13.50</p>
                            </div>
                            <div class="menu-item">
                                <div class="menu-item-image">🦐</div>
                                <h3>Seafood Linguine</h3>
                                <p class="menu-item-price">€16.99</p>
                            </div>
                        </div>
                    </div>

                    <!-- Extras & Beverages -->
                    <div class="category-section">
                        <h2 class="category-title">Extras & Beverages</h2>
                        <div class="menu-grid">
                            <div class="menu-item">
                                <div class="menu-item-image">🥗</div>
                                <h3>Caesar Salad</h3>
                                <p class="menu-item-price">€7.99</p>
                            </div>
                            <div class="menu-item">
                                <div class="menu-item-image">🍰</div>
                                <h3>Tiramisu</h3>
                                <p class="menu-item-price">€5.50</p>
                            </div>
                            <div class="menu-item">
                                <div class="menu-item-image">🥤</div>
                                <h3>Italian Soda</h3>
                                <p class="menu-item-price">€3.50</p>
                            </div>
                            <div class="menu-item">
                                <div class="menu-item-image">🍷</div>
                                <h3>House Wine</h3>
                                <p class="menu-item-price">€4.99</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Bottom Navigation -->
            <div class="bottom-nav">
                <div class="nav-item active">
                    <div class="nav-icon">🏠</div>
                    <div>Home</div>
                </div>
                <div class="nav-item">
                    <div class="nav-icon">🔍</div>
                    <div>Search</div>
                </div>
                <div class="nav-item">
                    <div class="nav-icon">❤️</div>
                    <div>Favorites</div>
                </div>
                <div class="nav-item">
                    <div class="nav-icon">👤</div>
                    <div>Profile</div>
                </div>
            </div>

            <!-- Floating Cart Button -->
            <div class="floating-cart">
                <span style="font-size: 24px;">🛒</span>
                <div class="cart-badge">3</div>
            </div>
        </div>
    </div>

    <script>
        function showPage(pageId) {
            // Hide all pages
            document.querySelectorAll('.page').forEach(page => {
                page.classList.remove('active');
            });
            
            // Show selected page
            document.getElementById(pageId).classList.add('active');
            
            // Update tab buttons
            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
        }

        // Add some interactivity to menu items
        document.querySelectorAll('.menu-item, .special-card, .popular-item').forEach(item => {
            item.addEventListener('click', function() {
                this.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    this.style.transform = '';
                }, 150);
            });
        });

        // Floating cart animation
        document.querySelector('.floating-cart').addEventListener('click', function() {
            this.style.transform = 'rotate(360deg) scale(1.2)';
            setTimeout(() => {
                this.style.transform = '';
            }, 300);
        });
    </script>
</body>
</html>